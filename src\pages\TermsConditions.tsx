import Header from "@/components/Header";
import Footer from "@/components/Footer";

const TermsConditions = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-16 lg:pt-20">
        <section className="w-full bg-white py-16 lg:py-20">
          <div className="max-w-content mx-auto px-6">
            <div className="text-center">
              <h1 className="text-3xl lg:text-4xl font-bold text-black mb-4">
                Terms & Conditions
              </h1>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Please read these terms and conditions carefully before using our services.
              </p>
            </div>
          </div>
        </section>

        <div className="mb-20">
          <div className="max-w-4xl mx-auto px-6">
            <div className="prose prose-lg max-w-none">
              <div className="bg-white rounded-xl p-8 border border-lumen-yellow/20 shadow-lg space-y-8">

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">1. Acceptance of Terms</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    By accessing and using LumenWorks services, you accept and agree to be bound by the terms and provision of this agreement.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">2. Services</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    LumenWorks provides technology consulting, software development, cloud infrastructure, and related digital services. We reserve the right to modify or discontinue services at any time.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">3. User Responsibilities</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">4. Intellectual Property</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    All content, features, and functionality of our services are owned by LumenWorks and are protected by copyright, trademark, and other intellectual property laws.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">5. Limitation of Liability</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    LumenWorks shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of our services.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">6. Governing Law</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    These terms shall be governed by and construed in accordance with the laws of the jurisdiction in which LumenWorks operates.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">7. Contact Information</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    If you have any questions about these Terms and Conditions, please contact <NAME_EMAIL>.
                  </p>
                </div>

                <div className="border-t border-lumen-yellow/20 pt-6">
                  <p className="text-sm text-lumen-mid-gray">
                    Last updated: January 2025
                  </p>
                </div>

              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default TermsConditions;
